<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>خرائط اليمن - Yemen GPS</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    
    <!-- يلحق ملفات CSS الأساسية -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/yemen-maps.css">
    <link rel="stylesheet" href="assets/css/place-details.css">
    <link rel="stylesheet" href="assets/css/current-location.css">
    <link rel="stylesheet" href="assets/css/filters.css">
    <link rel="stylesheet" href="assets/css/directions.css">
    
    <!-- CSS لنافذة معلومات المكان المفصلة (لضمان عملها من أي مكان) -->
    <style>
        /* نافذة معلومات المكان المفصلة (Google Maps Style) */
        .place-details-panel {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 400px;
            max-width: 90%;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            z-index: 1500;
            display: flex;
            flex-direction: column;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow: hidden;
        }

        .place-details-panel.active {
            transform: translateX(0);
        }

        .place-details-header {
            background-color: #f8f8f8;
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .place-header-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .place-header-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #444;
            transition: background-color 0.2s;
        }

        .place-header-btn:hover {
            background-color: #eee;
        }

        .place-search-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        #place-search-input {
            width: 100%;
            padding: 8px 40px 8px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        #place-search-clear,
        #place-search-btn {
            position: absolute;
            right: 8px;
            background: none;
            border: none;
            cursor: pointer;
            color: #777;
        }

        #place-search-clear {
            right: 32px;
        }

        .place-details-content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 16px;
        }

        .place-image-container {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        #place-main-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .place-image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
        }

        .place-image-gallery {
            position: absolute;
            bottom: 8px;
            left: 8px;
            right: 8px;
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .gallery-thumbnail {
            width: 50px;
            height: 50px;
            flex-shrink: 0;
            border-radius: 4px;
            overflow: hidden;
            border: 2px solid white;
            cursor: pointer;
        }

        .gallery-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .place-basic-info {
            padding: 16px;
            border-bottom: 1px solid #eee;
        }

        .place-basic-info h1 {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: bold;
        }

        .place-category {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #5f6368;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .place-rating {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .stars {
            color: #ffc107;
        }

        .rating-value {
            font-weight: bold;
        }

        .review-count {
            color: #5f6368;
            font-size: 14px;
        }

        .place-address {
            color: #5f6368;
            font-size: 14px;
        }

        .place-quick-actions {
            display: flex;
            justify-content: space-around;
            padding: 16px;
            border-bottom: 1px solid #eee;
        }

        .quick-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            color: #1a73e8;
        }

        .quick-action-btn i {
            font-size: 20px;
        }

        .quick-action-btn span {
            font-size: 12px;
        }

        .place-info-section {
            padding: 16px;
            border-bottom: 1px solid #eee;
        }

        .place-info-section h3 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: bold;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .contact-item i {
            color: #5f6368;
            width: 20px;
            text-align: center;
        }

        .contact-item a {
            color: #1a73e8;
            text-decoration: none;
        }

        .hours-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .current-day {
            font-weight: bold;
            color: #1a73e8;
        }

        .reviews-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
    </style>
    
    <style>
        /* Base Styles */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html, body {
            height: 100%;
            width: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            overflow: hidden;
        }
        
        /* Map Container */
        #map-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
        }
        
        #map {
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        /* Search Bar */
        .search-container {
            position: absolute;
            top: 16px;
            right: 16px;
            left: 16px;
            max-width: 480px;
            margin: 0 auto;
            z-index: 1000;
        }
        
        .search-box {
            display: flex;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            padding: 8px 16px;
            width: 100%;
        }
        
        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 8px;
        }
        
        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #555;
        }
        
        /* Map Controls */
        .map-controls {
            position: absolute;
            bottom: 24px;
            right: 16px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .map-control-btn {
            width: 40px;
            height: 40px;
            background-color: white;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #555;
            font-size: 16px;
        }
        
        .map-control-btn:hover {
            background-color: #f5f5f5;
        }
        
        /* Layer Controls */
        .layer-controls {
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 1000;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }
        
        .layer-btn {
            display: block;
            padding: 8px 16px;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
            cursor: pointer;
            font-size: 14px;
            border-bottom: 1px solid #eee;
        }
        
        .layer-btn:last-child {
            border-bottom: none;
        }
        
        .layer-btn.active {
            background-color: #f0f7ff;
            color: #1a73e8;
            font-weight: bold;
        }
        
        .layer-btn:hover:not(.active) {
            background-color: #f5f5f5;
        }
        
        /* Popup Styles */
        .custom-popup {
            min-width: 220px;
            max-width: 300px;
            direction: rtl;
        }
        
        .custom-popup img {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        
        .custom-popup h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            color: #333;
        }
        
        .custom-popup p {
            margin: 4px 0;
            font-size: 14px;
            color: #555;
        }
        
        .popup-actions {
            display: flex;
            gap: 16px;
            margin-top: 12px;
            justify-content: flex-start;
        }
        
        .popup-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            color: #1a73e8;
            font-size: 12px;
        }
        
        .popup-action-btn i {
            font-size: 18px;
        }
        
        /* Loading Indicator */
        #loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
        }
        
        .spinner {
            width: 32px;
            height: 32px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Bottom Info Panel (for directions) */
        #directions-panel {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 200px;
            background-color: white;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        #directions-panel.active {
            transform: translateY(0);
        }
        
        .directions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
        }
        
        .directions-content {
            padding: 16px;
            height: calc(100% - 50px);
            overflow-y: auto;
        }
        
        .direction-step {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
            align-items: flex-start;
        }
        
        .direction-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f1f3f4;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .direction-text {
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- Search Bar -->
    <div class="search-container">
        <div class="search-box">
            <input type="text" id="search-input" class="search-input" placeholder="ابحث عن موقع أو عنوان...">
            <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
        </div>
    </div>
    
    <!-- Map Container -->
    <div id="map-container">
        <div id="map"></div>
        
        <!-- أزرار تبديل نوع الخريطة -->
        <div class="map-layer-controls">
            <button class="map-layer-btn active" data-layer="streets" title="خريطة الشوارع">
                <i class="fas fa-road"></i>
            </button>
            <button class="map-layer-btn" data-layer="satellite" title="خريطة الأقمار الصناعية">
                <i class="fas fa-satellite"></i>
            </button>
            <button class="map-layer-btn" data-layer="terrain" title="خريطة التضاريس">
                <i class="fas fa-mountain"></i>
            </button>
        </div>
        
        <!-- أزرار التحكم بالخريطة -->
        <div class="map-controls">
            <button id="my-location-btn" class="map-control-btn" title="تحديد موقعي">
                <i class="fas fa-location-crosshairs"></i>
            </button>
            <button id="zoom-in-btn" class="map-control-btn" title="تكبير">
                <i class="fas fa-plus"></i>
            </button>
            <button id="zoom-out-btn" class="map-control-btn" title="تصغير">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    </div>
    
    <!-- مؤشر التحميل -->
    <div id="loading-indicator">
        <div class="loading-spinner"></div>
    </div>
    

    

    
    <!-- نافذة معلومات المكان المفصلة (Google Maps Style) -->
    <div id="place-details-panel" class="place-details-panel">
        <div class="place-details-header">
            <div class="place-header-actions">
                <button id="close-place-details-btn" class="place-header-btn"><i class="fas fa-arrow-right"></i></button>
                <button id="share-place-btn" class="place-header-btn"><i class="fas fa-share-alt"></i></button>
                <button id="save-place-btn" class="place-header-btn"><i class="far fa-bookmark"></i></button>
            </div>
            <div class="place-search-container">
                <input type="text" id="place-search-input" placeholder="ابحث عن مكان...">
                <button id="place-search-clear"><i class="fas fa-times"></i></button>
                <button id="place-search-btn"><i class="fas fa-search"></i></button>
            </div>
        </div>
        
        <div class="place-details-content">
            <!-- صورة المكان الرئيسية -->
            <div class="place-image-container">
                <img id="place-main-image" src="" alt="صورة المكان" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">
                <div class="place-image-overlay"></div>
                <div class="place-image-gallery" id="place-image-gallery">
                    <!-- سيتم إضافة معرض الصور هنا ديناميكيًا -->
                </div>
            </div>
            
            <!-- معلومات المكان الأساسية -->
            <div class="place-basic-info">
                <h1 id="place-name">اسم المكان</h1>
                <div class="place-category" id="place-category"><i class="fas fa-map-marker-alt"></i> <span>موقع</span></div>
                <div class="place-rating" id="place-rating">
                    <div class="stars">★★★★☆</div>
                    <span class="rating-value">4.5</span>
                    <span class="review-count">(123 تقييم)</span>
                </div>
                <div class="place-address" id="place-address">العنوان</div>
            </div>
            
            <!-- أزرار الإجراءات السريعة -->
            <div class="place-quick-actions">
                <button id="directions-btn" class="quick-action-btn">
                    <i class="fas fa-directions"></i>
                    <span>المسار</span>
                </button>
                <button id="call-btn" class="quick-action-btn">
                    <i class="fas fa-phone"></i>
                    <span>اتصال</span>
                </button>
                <button id="website-btn" class="quick-action-btn">
                    <i class="fas fa-globe"></i>
                    <span>الموقع</span>
                </button>
                <button id="more-btn" class="quick-action-btn">
                    <i class="fas fa-ellipsis-h"></i>
                    <span>المزيد</span>
                </button>
            </div>
            
            <!-- معلومات الاتصال -->
            <div class="place-contact-info">
                <div class="place-info-section">
                    <h3>معلومات الاتصال</h3>
                    <div class="contact-item" id="contact-phone">
                        <i class="fas fa-phone"></i>
                        <span>رقم الهاتف</span>
                    </div>
                    <div class="contact-item" id="contact-website">
                        <i class="fas fa-globe"></i>
                        <span>الموقع الإلكتروني</span>
                    </div>
                    <div class="contact-item" id="contact-address">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>العنوان</span>
                    </div>
                </div>
            </div>
            
            <!-- ساعات العمل -->
            <div class="place-opening-hours" id="place-opening-hours">
                <div class="place-info-section">
                    <h3>ساعات العمل</h3>
                    <div class="hours-list" id="hours-list">
                        <!-- سيتم إضافة ساعات العمل هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
            
            <!-- حقائق سريعة -->
            <div class="place-quick-facts">
                <div class="place-info-section">
                    <h3>حقائق سريعة</h3>
                    <p id="place-description">وصف المكان سيظهر هنا.</p>
                </div>
            </div>
            
            <!-- المراجعات والتقييمات -->
            <div class="place-reviews">
                <div class="place-info-section">
                    <h3>المراجعات</h3>
                    <div class="reviews-list" id="reviews-list">
                        <!-- سيتم إضافة المراجعات هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نافذة معلومات المكان المفصلة (Google Maps Style) -->
    <div id="place-details-panel" class="place-details-panel">
        <div class="place-details-header">
            <div class="place-header-actions">
                <button id="close-place-details-btn" class="place-header-btn"><i class="fas fa-arrow-right"></i></button>
                <button id="share-place-btn" class="place-header-btn"><i class="fas fa-share-alt"></i></button>
                <button id="save-place-btn" class="place-header-btn"><i class="far fa-bookmark"></i></button>
            </div>
            <div class="place-search-container">
                <input type="text" id="place-search-input" placeholder="ابحث عن مكان...">
                <button id="place-search-clear"><i class="fas fa-times"></i></button>
                <button id="place-search-btn"><i class="fas fa-search"></i></button>
            </div>
        </div>
        
        <div class="place-details-content">
            <!-- صورة المكان الرئيسية -->
            <div class="place-image-container">
                <img id="place-main-image" src="" alt="صورة المكان" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">
                <div class="place-image-overlay"></div>
                <div class="place-image-gallery" id="place-image-gallery">
                    <!-- سيتم إضافة معرض الصور هنا ديناميكيًا -->
                </div>
            </div>
            
            <!-- معلومات المكان الأساسية -->
            <div class="place-basic-info">
                <h1 id="place-name">اسم المكان</h1>
                <div class="place-category" id="place-category"><i class="fas fa-map-marker-alt"></i> <span>موقع</span></div>
                <div class="place-rating" id="place-rating">
                    <div class="stars">★★★★☆</div>
                    <span class="rating-value">4.5</span>
                    <span class="review-count">(123 تقييم)</span>
                </div>
                <div class="place-address" id="place-address">العنوان</div>
            </div>
            
            <!-- أزرار الإجراءات السريعة -->
            <div class="place-quick-actions">
                <button id="directions-btn" class="quick-action-btn">
                    <i class="fas fa-directions"></i>
                    <span>المسار</span>
                </button>
                <button id="call-btn" class="quick-action-btn">
                    <i class="fas fa-phone"></i>
                    <span>اتصال</span>
                </button>
                <button id="website-btn" class="quick-action-btn">
                    <i class="fas fa-globe"></i>
                    <span>الموقع</span>
                </button>
                <button id="more-btn" class="quick-action-btn">
                    <i class="fas fa-ellipsis-h"></i>
                    <span>المزيد</span>
                </button>
            </div>
            
            <!-- معلومات الاتصال -->
            <div class="place-contact-info">
                <div class="place-info-section">
                    <h3>معلومات الاتصال</h3>
                    <div class="contact-item" id="contact-phone">
                        <i class="fas fa-phone"></i>
                        <span>رقم الهاتف</span>
                    </div>
                    <div class="contact-item" id="contact-website">
                        <i class="fas fa-globe"></i>
                        <span>الموقع الإلكتروني</span>
                    </div>
                    <div class="contact-item" id="contact-address">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>العنوان</span>
                    </div>
                </div>
            </div>
            
            <!-- ساعات العمل -->
            <div class="place-opening-hours" id="place-opening-hours">
                <div class="place-info-section">
                    <h3>ساعات العمل</h3>
                    <div class="hours-list" id="hours-list">
                        <!-- سيتم إضافة ساعات العمل هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
            
            <!-- حقائق سريعة -->
            <div class="place-quick-facts">
                <div class="place-info-section">
                    <h3>حقائق سريعة</h3>
                    <p id="place-description">وصف المكان سيظهر هنا.</p>
                </div>
            </div>
            
            <!-- المراجعات والتقييمات -->
            <div class="place-reviews">
                <div class="place-info-section">
                    <h3>المراجعات</h3>
                    <div class="reviews-list" id="reviews-list">
                        <!-- سيتم إضافة المراجعات هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    
    <!-- ملف عرض تفاصيل المكان -->
    <script src="assets/js/place-details.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/yemen-db-helper.js"></script>
    <script src="assets/js/yemen-maps.js"></script>
</body>
</html>
