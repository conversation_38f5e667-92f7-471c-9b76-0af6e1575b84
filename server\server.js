const express = require('express');
const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const port = 5000;

// إعداد قاعدة البيانات
const db = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin',
});

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  optionsSuccessStatus: 200
}));

// تحسين الأداء
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// خدمة الملفات الثابتة مع خيارات ذاكرة التخزين المؤقت
const cacheTime = 86400000 * 30; // 30 يوم
app.use(express.static(path.join(__dirname, '../public'), {
  maxAge: cacheTime,
  etag: true,
  lastModified: true
}));

// للوصول إلى الملفات في المجلد الجذر
app.use(express.static(path.join(__dirname, '..'), {
  maxAge: cacheTime,
  etag: true,
  lastModified: true
}));

// إضافة رأس الأمان
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  next();
});

// اختبار الاتصال بقاعدة البيانات
db.connect()
  .then(() => console.log('✅ تم الاتصال بقاعدة البيانات بنجاح'))
  .catch(err => console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err));

// مسار API لإرجاع المواقع (وهمي إذا لم تتوفر قاعدة البيانات)
app.get('/api/locations', async (req, res) => {
  try {
    // جرب جلب المواقع من قاعدة البيانات
    const result = await db.query('SELECT id, name, latitude, longitude, address FROM locations');
    return res.json(result.rows);
  } catch (err) {
    // إذا فشل الاتصال بقاعدة البيانات، أرجع بيانات وهمية
    return res.json([
      { id: 1, name: 'ميدان السبعين', latitude: 15.3136, longitude: 44.1867, address: 'صنعاء' },
      { id: 2, name: 'قلعة القاهرة', latitude: 13.5789, longitude: 44.0209, address: 'تعز' },
      { id: 3, name: 'مدينة سيئون', latitude: 15.9431, longitude: 48.7879, address: 'حضرموت' }
    ]);
  }
});

// المسارات الأساسية
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/admin.html'));
});

// API تسجيل الدخول
app.post('/api/login', async (req, res) => {
  const { username, password } = req.body;
  
  try {
    console.log('محاولة تسجيل دخول للمستخدم:', username);
    
    const result = await db.query('SELECT * FROM users WHERE username = $1', [username]);
    
    if (result.rows.length === 0) {
      console.log('المستخدم غير موجود:', username);
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    const user = result.rows[0];
    console.log('تم العثور على المستخدم:', user.username);
    
    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      console.log('كلمة المرور غير صحيحة للمستخدم:', username);
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    console.log('تم تسجيل الدخول بنجاح للمستخدم:', username);
    
    // إرسال بيانات المستخدم (بدون كلمة المرور)
    const userData = {
      user_id: user.id,
      username: user.username,
      full_name: user.full_name,
      email: user.email,
      phone: user.phone,
      role_id: user.role_id,
      is_active: user.is_active
    };
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      user: userData,
      token: 'dummy-token-' + Date.now()
    });
    
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err);
    res.status(500).json({ message: 'حدث خطأ في الخادم' });
  }
});

// API المستخدمين
app.get('/api/users', async (req, res) => {
  try {
    console.log('جلب قائمة المستخدمين...');
    const result = await db.query(`
      SELECT id, username, full_name, email, phone, role_id, is_active, registration_date, last_login, type
      FROM users 
      ORDER BY id
    `);
    console.log('تم جلب', result.rows.length, 'مستخدم');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب المستخدمين:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// API التصنيفات
app.get('/api/categories', async (req, res) => {
  try {
    console.log('جلب قائمة التصنيفات...');
    const result = await db.query('SELECT * FROM categories ORDER BY id');
    console.log('تم جلب', result.rows.length, 'تصنيف');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب التصنيفات:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات التصنيفات' });
  }
});

// API المواقع للأدمن
app.get('/api/admin/locations', async (req, res) => {
  try {
    console.log('جلب قائمة المواقع للأدمن...');
    const result = await db.query(`
      SELECT l.*, c.name as category_name 
      FROM locations l 
      LEFT JOIN categories c ON l.category_id = c.id 
      ORDER BY l.id
    `);
    console.log('تم جلب', result.rows.length, 'موقع');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب المواقع:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المواقع' });
  }
});

// API المواقع للعملاء
app.get('/api/locations', async (req, res) => {
  try {
    console.log('جلب قائمة المواقع للعملاء...');
    const result = await db.query(`
      SELECT 
        id,
        name,
        description,
        latitude,
        longitude,
        address,
        city,
        phone as contact,
        email,
        website,
        opening_hours as hours,
        category_id,
        place_id
      FROM locations 
      WHERE is_active = true
      ORDER BY name
    `);
    console.log('تم جلب', result.rows.length, 'موقع للعملاء');
    
    // في حالة عدم وجود مواقع في قاعدة البيانات، تقديم بيانات افتراضية
    if (result.rows.length === 0) {
      const defaultLocations = [
        { 
          id: 1, 
          name: 'ميدان السبعين', 
          latitude: 15.3136, 
          longitude: 44.1867, 
          address: 'صنعاء', 
          city: 'صنعاء',
          contact: '777123456',
          description: 'ميدان السبعين هو أحد أشهر ميادين صنعاء، ويعد من أهم المعالم المميزة في العاصمة.',
          place_id: 'ChIJ5UfYV33FAxYRV_jxCFmR3Ic'
        },
        { 
          id: 2, 
          name: 'قلعة القاهرة', 
          latitude: 13.5789, 
          longitude: 44.0209, 
          address: 'تعز', 
          city: 'تعز',
          contact: '777123457',
          description: 'قلعة تاريخية شهيرة تقع على قمة جبل صبر وتطل على مدينة تعز، بنيت في العصر الأيوبي.',
          place_id: 'ChIJ7YCGE1HfAxYRVKLZ_Gtxhgg'
        },
        { 
          id: 3, 
          name: 'مدينة سيئون', 
          latitude: 15.9431, 
          longitude: 48.7879, 
          address: 'حضرموت', 
          city: 'حضرموت',
          contact: '777123458',
          description: 'مدينة تاريخية في وادي حضرموت، تشتهر بقصورها الطينية ومبانيها التاريخية المميزة.',
          place_id: 'ChIJ4862o_PaAxYRaZqhQwhe_pQ'
        },
        { 
          id: 4, 
          name: 'جزيرة سقطرى', 
          latitude: 12.4634, 
          longitude: 53.8250, 
          address: 'أرخبيل سقطرى', 
          city: 'سقطرى',
          contact: '777123459',
          description: 'أرخبيل يقع في المحيط الهندي، معروف بطبيعته الفريدة والنباتات النادرة مثل شجرة دم التنين.',
          place_id: 'ChIJ6Y_taubbAxYRyBkYlWUiblo'
        },
        { 
          id: 5, 
          name: 'قلعة صيرة', 
          latitude: 12.7852, 
          longitude: 45.0281, 
          address: 'عدن', 
          city: 'عدن',
          contact: '777123460',
          description: 'قلعة تاريخية بناها العثمانيون، تقع على جزيرة صغيرة في خليج عدن.',
          place_id: 'ChIJ78UibgDbAxYRfa7kzpC4G2Y'
        }
      ];
      console.log('لا توجد مواقع في قاعدة البيانات، تم تقديم بيانات افتراضية');
      res.json(defaultLocations);
      return;
    }
    
    // إضافة مسارات الصور للمواقع
    const locationsWithImages = result.rows.map(location => {
      const images = [];
      if (location.place_id) {
        // البحث عن صور المكان في مجلد images/places
        const fs = require('fs');
        const placesDir = path.join(__dirname, '../public/images/places');
        try {
          if (fs.existsSync(placesDir)) {
            const files = fs.readdirSync(placesDir);
            // البحث عن الصور التي تبدأ بمعرف المكان
            const placeImages = files.filter(file => file.startsWith(location.place_id));
            // ترتيب الصور حسب الرقم التسلسلي (_0, _1, إلخ)
            placeImages.sort((a, b) => {
              const numA = parseInt(a.split('_')[1]);
              const numB = parseInt(b.split('_')[1]);
              return numA - numB;
            });
            // إضافة مسارات الصور
            placeImages.forEach(img => {
              images.push(`/images/places/${img}`);
            });
          }
        } catch (fsErr) {
          console.error('خطأ في قراءة مجلد الصور:', fsErr);
        }
      }
      
      return {
        ...location,
        images: images.length > 0 ? images : (location.images || [])
      };
    });
    
    res.json(locationsWithImages);
  } catch (err) {
    console.error('خطأ في جلب المواقع للعملاء:', err);
    
    // في حالة الخطأ، تقديم بيانات افتراضية مع صور
    const defaultLocations = [
      { 
        id: 1, 
        name: 'ميدان السبعين', 
        latitude: 15.3136, 
        longitude: 44.1867, 
        address: 'صنعاء', 
        city: 'صنعاء',
        contact: '777123456',
        description: 'ميدان السبعين هو أحد أشهر ميادين صنعاء، ويعد من أهم المعالم المميزة في العاصمة.',
        place_id: 'ChIJ5UfYV33FAxYRV_jxCFmR3Ic',
        images: ['/images/places/ChIJ5UfYV33FAxYRV_jxCFmR3Ic_0.jpg', '/images/places/ChIJ5UfYV33FAxYRV_jxCFmR3Ic_1.jpg']
      },
      { 
        id: 2, 
        name: 'قلعة القاهرة', 
        latitude: 13.5789, 
        longitude: 44.0209, 
        address: 'تعز', 
        city: 'تعز',
        contact: '777123457',
        description: 'قلعة تاريخية شهيرة تقع على قمة جبل صبر وتطل على مدينة تعز، بنيت في العصر الأيوبي.',
        place_id: 'ChIJ7YCGE1HfAxYRVKLZ_Gtxhgg',
        images: ['/images/places/ChIJ7YCGE1HfAxYRVKLZ_Gtxhgg_0.jpg', '/images/places/ChIJ7YCGE1HfAxYRVKLZ_Gtxhgg_1.jpg']
      },
      { 
        id: 3, 
        name: 'مدينة سيئون', 
        latitude: 15.9431, 
        longitude: 48.7879, 
        address: 'حضرموت', 
        city: 'حضرموت',
        contact: '777123458',
        description: 'مدينة تاريخية في وادي حضرموت، تشتهر بقصورها الطينية ومبانيها التاريخية المميزة.',
        place_id: 'ChIJ4862o_PaAxYRaZqhQwhe_pQ',
        images: ['/images/places/ChIJ4862o_PaAxYRaZqhQwhe_pQ_0.jpg', '/images/places/ChIJ4862o_PaAxYRaZqhQwhe_pQ_1.jpg']
      },
      { 
        id: 4, 
        name: 'جزيرة سقطرى', 
        latitude: 12.4634, 
        longitude: 53.8250, 
        address: 'أرخبيل سقطرى', 
        city: 'سقطرى',
        contact: '777123459',
        description: 'أرخبيل يقع في المحيط الهندي، معروف بطبيعته الفريدة والنباتات النادرة مثل شجرة دم التنين.',
        place_id: 'ChIJ6Y_taubbAxYRyBkYlWUiblo',
        images: ['/images/places/ChIJ6Y_taubbAxYRyBkYlWUiblo_0.jpg', '/images/places/ChIJ6Y_taubbAxYRyBkYlWUiblo_1.jpg']
      },
      { 
        id: 5, 
        name: 'قلعة صيرة', 
        latitude: 12.7852, 
        longitude: 45.0281, 
        address: 'عدن', 
        city: 'عدن',
        contact: '777123460',
        description: 'قلعة تاريخية بناها العثمانيون، تقع على جزيرة صغيرة في خليج عدن.',
        place_id: 'ChIJ78UibgDbAxYRfa7kzpC4G2Y',
        images: ['/images/places/ChIJ78UibgDbAxYRfa7kzpC4G2Y_0.jpg', '/images/places/ChIJ78UibgDbAxYRfa7kzpC4G2Y_1.jpg']
      }
    ];
    console.log('حدث خطأ في قاعدة البيانات، تم تقديم بيانات افتراضية');
    res.json(defaultLocations);
  }
});

// API العملاء
app.get('/api/clients', async (req, res) => {
  try {
    console.log('جلب قائمة العملاء...');
    const result = await db.query('SELECT * FROM clients ORDER BY id');
    console.log('تم جلب', result.rows.length, 'عميل');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب العملاء:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات العملاء' });
  }
});

// API اختبار الاتصال
app.get('/api/test-connection', async (req, res) => {
  try {
    const result = await db.query('SELECT NOW() as current_time');
    res.json({ 
      message: 'الاتصال بقاعدة البيانات يعمل بنجاح', 
      timestamp: result.rows[0].current_time 
    });
  } catch (err) {
    console.error('خطأ في اختبار الاتصال:', err);
    res.status(500).json({ message: 'فشل الاتصال بقاعدة البيانات' });
  }
});

// API حفظ المواقع المفضلة
app.post('/api/save-place', async (req, res) => {
  try {
    const { location_id, user_id, saved_at } = req.body;
    
    if (!location_id || !user_id) {
      return res.status(400).json({ message: 'البيانات غير مكتملة' });
    }
    
    // تحقق من وجود المكان في قاعدة البيانات
    const checkLocation = await db.query('SELECT id FROM locations WHERE id = $1', [location_id]);
    
    if (checkLocation.rows.length === 0) {
      return res.status(404).json({ message: 'المكان غير موجود' });
    }
    
    // تحقق من وجود المستخدم في قاعدة البيانات
    let userId = user_id;
    if (user_id !== 'guest') {
      const checkUser = await db.query('SELECT id FROM users WHERE id = $1', [user_id]);
      if (checkUser.rows.length === 0) {
        userId = 'guest';
      }
    }
    
    // تحقق مما إذا كان المكان محفوظًا بالفعل
    const checkSaved = await db.query(
      'SELECT id FROM saved_places WHERE location_id = $1 AND user_id = $2',
      [location_id, userId]
    );
    
    if (checkSaved.rows.length > 0) {
      // إذا كان المكان محفوظًا بالفعل، قم بتحديثه
      await db.query(
        'UPDATE saved_places SET saved_at = $1 WHERE location_id = $2 AND user_id = $3',
        [saved_at || new Date().toISOString(), location_id, userId]
      );
      
      res.json({ message: 'تم تحديث حالة الحفظ' });
    } else {
      // إذا لم يكن المكان محفوظًا، قم بإضافته
      await db.query(
        'INSERT INTO saved_places (location_id, user_id, saved_at) VALUES ($1, $2, $3)',
        [location_id, userId, saved_at || new Date().toISOString()]
      );
      
      res.json({ message: 'تم حفظ المكان بنجاح' });
    }
  } catch (err) {
    console.error('خطأ في حفظ المكان:', err);
    res.status(500).json({ message: 'حدث خطأ في حفظ المكان' });
  }
});

// API اختبار الخادم
app.get('/test', (req, res) => {
  res.send('صفحة الاختبار تعمل بنجاح!');
});

// API للحصول على تفاصيل مكان من Google
app.get('/api/google-place-details', async (req, res) => {
  const placeId = req.query.place_id;
  
  if (!placeId) {
    return res.status(400).json({ error: 'يجب توفير معرف المكان (place_id)' });
  }
  
  try {
    // في الواقع يجب أن تقوم بطلب البيانات من Google Places API
    // هنا نقدم بيانات إفتراضية للعرض
    const mockPlaceDetails = {
      name: 'موقع إفتراضي',
      formatted_address: 'عنوان إفتراضي للموقع',
      formatted_phone_number: '+967 123 456 789',
      website: 'https://example.com',
      rating: 4.5,
      opening_hours: {
        weekday_text: [
          'Monday: 9:00 AM – 10:00 PM',
          'Tuesday: 9:00 AM – 10:00 PM',
          'Wednesday: 9:00 AM – 10:00 PM',
          'Thursday: 9:00 AM – 10:00 PM',
          'Friday: 2:00 PM – 10:00 PM',
          'Saturday: 9:00 AM – 11:00 PM',
          'Sunday: 9:00 AM – 10:00 PM'
        ]
      },
      types: ['restaurant', 'point_of_interest', 'establishment'],
      editorial_summary: 'معلومات مفصلة عن الموقع الإفتراضي.',
      reviews: [
        {
          author_name: 'مستخدم 1',
          rating: 5,
          text: 'مكان رائع وخدمة ممتازة!',
          time: new Date().getTime() / 1000
        },
        {
          author_name: 'مستخدم 2',
          rating: 4,
          text: 'تجربة جيدة جداً.',
          time: new Date().getTime() / 1000 - 86400
        }
      ]
    };
    
    return res.json(mockPlaceDetails);
  } catch (err) {
    console.error('خطأ في جلب تفاصيل Google Places:', err);
    return res.status(500).json({ error: 'خطأ في جلب تفاصيل المكان' });
  }
});

// API للحصول على صور مكان من Google
app.get('/api/google-place-photos', async (req, res) => {
  const placeId = req.query.place_id;
  const maxPhotos = parseInt(req.query.max_photos || '5');
  
  if (!placeId) {
    return res.status(400).json({ error: 'يجب توفير معرف المكان (place_id)' });
  }
  
  try {
    // البحث عن صور المكان من مجلد الصور
    const fs = require('fs');
    const path = require('path');
    const placesImagesDir = path.join(__dirname, '../public/images/places');
    
    // التحقق من وجود مجلد الصور
    if (!fs.existsSync(placesImagesDir)) {
      console.warn('مجلد الصور غير موجود:', placesImagesDir);
      return res.json(['/assets/images/placeholder.jpg']);
    }
    
    // قراءة محتويات المجلد
    const files = fs.readdirSync(placesImagesDir);
    
    // البحث عن صور مطابقة لمعرف المكان
    const placePhotos = files
      .filter(file => file.startsWith(placeId + '_') && (file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.jpeg') || file.endsWith('.gif')))
      .sort((a, b) => {
        // ترتيب الصور حسب رقمها
        const indexA = parseInt(a.split('_')[1].split('.')[0]) || 0;
        const indexB = parseInt(b.split('_')[1].split('.')[0]) || 0;
        return indexA - indexB;
      })
      .slice(0, maxPhotos)
      .map(file => `/images/places/${file}`);
    
    // إذا لم يتم العثور على صور، استخدم صورة افتراضية
    if (placePhotos.length === 0) {
      return res.json(['/assets/images/placeholder.jpg']);
    }
    
    return res.json(placePhotos);
  } catch (err) {
    console.error('خطأ في جلب صور Google Places:', err);
    return res.status(500).json({ error: 'خطأ في جلب صور المكان' });
  }
});

// إضافة نقطة جديدة من Google Places
app.post('/api/add-google-place', async (req, res) => {
  const { place_id, name, lat, lng, address, types } = req.body;
  
  if (!place_id || !name || !lat || !lng) {
    return res.status(400).json({ error: 'البيانات غير كاملة' });
  }
  
  try {
    // إضافة المكان الجديد (للتبسيط نعود معرف وهمي)
    const mockLocationId = 'loc_' + Date.now();
    return res.json({ success: true, message: 'تمت إضافة المكان بنجاح', location_id: mockLocationId });
  } catch (err) {
    console.error('خطأ في إضافة مكان من Google Places:', err);
    return res.status(500).json({ error: 'خطأ في إضافة المكان' });
  }
});

// الحصول على قائمة الفئات لصفحة places.html
app.get('/api/places/categories', async (req, res) => {
  try {
    // قائمة الفئات الوهمية
    const mockCategories = [
      { id: 1, name_en: 'Restaurant', name_ar: 'مطعم', icon: 'fa-utensils' },
      { id: 2, name_en: 'Hotel', name_ar: 'فندق', icon: 'fa-hotel' },
      { id: 3, name_en: 'Tourist Attraction', name_ar: 'معلم سياحي', icon: 'fa-monument' },
      { id: 4, name_en: 'Shopping', name_ar: 'تسوق', icon: 'fa-shopping-cart' },
      { id: 5, name_en: 'Services', name_ar: 'خدمات', icon: 'fa-concierge-bell' }
    ];
    
    return res.json({
      success: true,
      data: mockCategories
    });
  } catch (err) {
    console.error('خطأ في جلب قائمة الفئات:', err);
    return res.status(500).json({ success: false, error: 'خطأ في جلب قائمة الفئات' });
  }
});

// الحصول على قائمة المحافظات لصفحة places.html
app.get('/api/places/governorates', async (req, res) => {
  try {
    // قائمة المحافظات الوهمية
    const mockGovernorates = [
      { id: 1, name_en: 'Sana\'a', name_ar: 'صنعاء' },
      { id: 2, name_en: 'Aden', name_ar: 'عدن' },
      { id: 3, name_en: 'Taiz', name_ar: 'تعز' },
      { id: 4, name_en: 'Hadramaut', name_ar: 'حضرموت' },
      { id: 5, name_en: 'Socotra', name_ar: 'سقطرى' }
    ];
    
    return res.json({
      success: true,
      data: mockGovernorates
    });
  } catch (err) {
    console.error('خطأ في جلب قائمة المحافظات:', err);
    return res.status(500).json({ success: false, error: 'خطأ في جلب قائمة المحافظات' });
  }
});

// الحصول على قائمة الأماكن لصفحة places.html
app.get('/api/places/places', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit || '50');
    const category = req.query.category;
    const governorate = req.query.governorate;
    const searchTerm = req.query.search;
    
    // في الواقع نريد الاستعلام من قاعدة البيانات
    // لكن للعرض سنقوم بإنشاء بيانات وهمية
    
    // قائمة المحافظات الوهمية
    const mockGovernorates = [
      { id: 1, name_en: 'Sana\'a', name_ar: 'صنعاء' },
      { id: 2, name_en: 'Aden', name_ar: 'عدن' },
      { id: 3, name_en: 'Taiz', name_ar: 'تعز' },
      { id: 4, name_en: 'Hadramaut', name_ar: 'حضرموت' },
      { id: 5, name_en: 'Socotra', name_ar: 'سقطرى' }
    ];
    
    // قائمة الفئات الوهمية
    const mockCategories = [
      { id: 1, name_en: 'Restaurant', name_ar: 'مطعم', icon: 'fa-utensils' },
      { id: 2, name_en: 'Hotel', name_ar: 'فندق', icon: 'fa-hotel' },
      { id: 3, name_en: 'Tourist Attraction', name_ar: 'معلم سياحي', icon: 'fa-monument' },
      { id: 4, name_en: 'Shopping', name_ar: 'تسوق', icon: 'fa-shopping-cart' },
      { id: 5, name_en: 'Services', name_ar: 'خدمات', icon: 'fa-concierge-bell' }
    ];
    
    // قراءة معرفات Google Places من مجلد الصور
    const fs = require('fs');
    const path = require('path');
    const placesImagesDir = path.join(__dirname, '../public/images/places');
    
    // التحقق من وجود مجلد الصور
    let placeIds = [];
    if (fs.existsSync(placesImagesDir)) {
      const files = fs.readdirSync(placesImagesDir);
      
      // استخراج معرفات الأماكن الفريدة
      placeIds = [...new Set(
        files
          .filter(file => file.includes('_') && (file.endsWith('.jpg') || file.endsWith('.png')))
          .map(file => file.split('_')[0])
      )];
    }
    
    // قائمة المحافظات لتوزيع الأماكن عليها
    const governorates = [
      { id: 1, name_ar: 'صنعاء', coords: [15.3694, 44.1910] },
      { id: 2, name_ar: 'عدن', coords: [12.7797, 45.0095] },
      { id: 3, name_ar: 'تعز', coords: [13.5769, 44.0178] },
      { id: 4, name_ar: 'حضرموت', coords: [15.9503, 48.7933] },
      { id: 5, name_ar: 'سقطرى', coords: [12.5092, 53.8250] }
    ];
    
    // قائمة الفئات
    const categories = [
      { id: 1, name_ar: 'مطعم', icon: 'fa-utensils' },
      { id: 2, name_ar: 'فندق', icon: 'fa-hotel' },
      { id: 3, name_ar: 'معلم سياحي', icon: 'fa-monument' },
      { id: 4, name_ar: 'تسوق', icon: 'fa-shopping-cart' },
      { id: 5, name_ar: 'خدمات', icon: 'fa-concierge-bell' }
    ];
    
    // الأسماء الافتراضية للأماكن في كل محافظة
    const placeNamesByGovernorate = {
      1: [
        'ميدان السبعين', 'المدينة القديمة', 'جامع الكبير',
        'فندق سبأ', 'مطعم بيت اليمن', 'جبل نقم', 'سوق باب اليمن'
      ],
      2: [
        'قلعة صيرة', 'شاطئ الذهبية', 'شاطئ العربي',
        'متحف عدن', 'مطعم المندب', 'فندق عدن'
      ],
      3: [
        'قلعة القاهرة', 'جبل صبر', 'مسجد الأشرفية',
        'مطعم السلطان', 'فندق سفير'
      ],
      4: [
        'وادي حضرموت', 'قصر سيئون', 'مدينة شبام',
        'متحف حضرموت', 'مطعم الكندي'
      ],
      5: [
        'شاطئ سقطرى', 'جبل حقهير', 'أرخبيل سقطرى',
        'وادي ديرهوت', 'متنزه سقطرى'
      ]
    };
    
    // إنشاء قائمة الأماكن باستخدام معرفات الأماكن الحقيقية
    const mockPlaces = [];
    let id = 1;
    
    // إذا وجدت صور، أنشئ الأماكن بناءً على معرفات Google Places
    for (const placeId of placeIds) {
      // اختيار محافظة عشوائية
      const governorateId = Math.floor(Math.random() * 5) + 1;
      const governorate = governorates.find(g => g.id === governorateId);
      
      // إنشاء إحداثيات قريبة من مركز المحافظة
      const latitude = governorate.coords[0] + (Math.random() - 0.5) * 0.3;
      const longitude = governorate.coords[1] + (Math.random() - 0.5) * 0.3;
      
      // اختيار فئة عشوائية
      const categoryId = Math.floor(Math.random() * 5) + 1;
      
      // اختيار اسم من القائمة المناسبة للمحافظة
      const placeNames = placeNamesByGovernorate[governorateId];
      const name = placeNames[Math.floor(Math.random() * placeNames.length)];
      
      // توليد معلومات المكان
      mockPlaces.push({
        id: id++,
        name_ar: name,
        name_en: name, // يمكن إضافة ترجمة للاسم في المستقبل
        latitude,
        longitude,
        address: governorate.name_ar,
        description: `معلومات عن ${name} في ${governorate.name_ar}`,
        google_place_id: placeId,
        category_id: categoryId,
        governorate_id: governorateId,
        main_image: `/images/places/${placeId}_0.jpg`,
        rating: (3 + Math.random() * 2).toFixed(1),
        reviews_count: Math.floor(Math.random() * 300),
        is_featured: Math.random() > 0.7,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }
    
    // إذا لم يتم العثور على صور، استخدم الأماكن الافتراضية
    if (mockPlaces.length === 0) {
      mockPlaces.push(
        {
          id: 1,
          name_ar: 'ميدان السبعين',
          name_en: 'Sabaeen Square',
          latitude: 15.3136,
          longitude: 44.1867,
          address: 'صنعاء',
          description: 'ميدان مشهور في العاصمة صنعاء',
          google_place_id: 'ChIJxxxxxxx1',
          category_id: 3,
          governorate_id: 1,
          main_image: '/assets/images/placeholder.jpg',
          rating: 4.5,
          reviews_count: 123,
          is_featured: true,
          status: 'active',
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z'
        },
        {
          id: 2,
          name_ar: 'قلعة القاهرة',
          name_en: 'Cairo Castle',
          latitude: 13.5789,
          longitude: 44.0209,
          address: 'تعز',
          description: 'معلم تاريخي وسياحي مهم في مدينة تعز',
          google_place_id: 'ChIJxxxxxxx2',
          category_id: 3,
          governorate_id: 3,
          main_image: '/assets/images/placeholder.jpg',
          rating: 4.7,
          reviews_count: 89,
          is_featured: true,
          status: 'active',
          created_at: '2025-01-02T00:00:00Z',
          updated_at: '2025-01-02T00:00:00Z'
        }
      );
    }
    
    // إضافة بيانات وهمية أخرى للوصول إلى الحد المطلوب
    for (let i = 6; i <= limit; i++) {
      if (mockPlaces.length >= limit) break;
      
      const categoryId = Math.floor(Math.random() * 5) + 1;
      const governorateId = Math.floor(Math.random() * 5) + 1;
      const governorate = mockGovernorates.find(g => g.id === governorateId);
      
      mockPlaces.push({
        id: i,
        name_ar: `مكان ${i}`,
        name_en: `Place ${i}`,
        latitude: 15.3 + (Math.random() - 0.5) * 3,
        longitude: 44.2 + (Math.random() - 0.5) * 4,
        address: governorate ? governorate.name_ar : 'اليمن',
        description: `وصف للمكان رقم ${i}`,
        google_place_id: `ChIJxxxxxxx${i}`,
        category_id: categoryId,
        governorate_id: governorateId,
        main_image: '/assets/images/placeholder.jpg',
        rating: (3 + Math.random() * 2).toFixed(1),
        reviews_count: Math.floor(Math.random() * 200),
        is_featured: Math.random() > 0.8,
        status: 'active',
        created_at: '2025-01-05T00:00:00Z',
        updated_at: '2025-01-05T00:00:00Z'
      });
    }
    
    // تطبيق الفلاتر
    let filteredPlaces = [...mockPlaces];
    
    if (category) {
      filteredPlaces = filteredPlaces.filter(place => place.category_id.toString() === category);
    }
    
    if (governorate) {
      filteredPlaces = filteredPlaces.filter(place => place.governorate_id.toString() === governorate);
    }
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filteredPlaces = filteredPlaces.filter(place => 
        place.name_ar.toLowerCase().includes(term) || 
        place.name_en.toLowerCase().includes(term) ||
        place.description.toLowerCase().includes(term));
    }
    
    // لكل مكان، أضف اسم الفئة والمحافظة
    const enrichedPlaces = filteredPlaces.map(place => {
      const category = mockCategories.find(c => c.id === place.category_id);
      const governorate = mockGovernorates.find(g => g.id === place.governorate_id);
      
      return {
        ...place,
        category_name: category ? category.name_ar : '',
        category_icon: category ? category.icon : 'fa-map-marker-alt',
        governorate_name: governorate ? governorate.name_ar : ''
      };
    });
    
    // إرجاع البيانات
    return res.json({
      success: true,
      data: enrichedPlaces,
      categories: mockCategories,
      governorates: mockGovernorates,
      total: enrichedPlaces.length,
      page: 1,
      limit: limit
    });
  } catch (err) {
    console.error('خطأ في جلب قائمة الأماكن:', err);
    return res.status(500).json({ success: false, error: 'خطأ في جلب قائمة الأماكن' });
  }
});

// تشغيل الخادم
app.listen(port, '0.0.0.0', () => {
  // الحصول على عنوان IP للجهاز
  const { networkInterfaces } = require('os');
  const nets = networkInterfaces();
  const ipAddresses = [];

  // استخراج جميع عناوين IP للجهاز
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // استخراج عناوين IPv4 فقط
      if (net.family === 'IPv4' && !net.internal) {
        ipAddresses.push(net.address);
      }
    }
  }

  console.log(`🚀 الخادم يعمل على المنفذ ${port} على جميع الشبكات (0.0.0.0)`);
  console.log(`🌐 افتح المتصفح محلياً على: http://localhost:${port}`);

  // طباعة جميع عناوين IP للوصول من الشبكة
  if (ipAddresses.length > 0) {
    console.log(`🌐 للوصول من أجهزة أخرى على نفس الشبكة:`);
    ipAddresses.forEach(ip => {
      console.log(`   - http://${ip}:${port}`);
    });
  } else {
    console.log(`⚠️ لم يتم العثور على عناوين IP للشبكة. قد تكون غير متصل بشبكة.`);
  }
  
  console.log(`👨‍💼 لوحة التحكم: http://localhost:${port}/admin-login.html`);
});
